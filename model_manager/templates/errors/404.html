<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - 模型管理服务</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
    .error-template {
        padding: 40px 15px;
        text-align: center;
    }
    .error-template h1 {
        font-size: 200px;
        font-weight: bold;
        color: #dc3545;
    }
    .error-template h2 {
        font-size: 24px;
        font-weight: normal;
        color: #333;
    }
    .error-details {
        font-size: 16px;
        color: #666;
    }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-cogs"></i> 模型管理服务
            </a>
            <span class="navbar-text">
                <span class="badge bg-danger">404 Error</span>
            </span>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6 text-center">
                <div class="error-template">
                    <h1>404</h1>
                    <h2>页面未找到</h2>
                    <div class="error-details">
                        抱歉，您访问的页面不存在。
                    </div>
                    <div class="error-actions mt-4">
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home"></i> 返回首页
                        </a>
                        <a href="javascript:history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回上页
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
